# React Integration Guide

## Create Demo Project

```shell
# node --version
# v24.2.0

npm create vite@latest betswirl-ui-react-demo -- --template react-ts
cd betswirl-ui-react-demo
npm install
```

## Verify Installation

```shell
npm run dev
# Open http://localhost:5173
```

## Add BetSwirl

### Install

```shell
npm install @betswirl/ui
```

Package: [npmjs.com/package/@betswirl/ui](https://www.npmjs.com/package/@betswirl/ui)

### Update main.tsx

Add all providers directly or create an AppProviders component:

```tsx
import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import { OnchainKitProvider, type AppConfig } from '@coinbase/onchainkit'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { http, type Hex } from 'viem'
import { WagmiProvider, createConfig } from 'wagmi'
import { base } from 'wagmi/chains'
import { BetSwirlSDKProvider, type TokenWithImage } from '@betswirl/ui'
import '@betswirl/ui/styles.css'
import './index.css'
import App from './App.tsx'

const queryClient = new QueryClient()
const config = createConfig({
  chains: [base],
  transports: {
    [base.id]: http(),
  },
})

const onChainKitConfig: AppConfig = {
  wallet: {
    display: "modal",
  }
}

// Optional: Define tokens for your application
const DEGEN_TOKEN: TokenWithImage = {
  address: "******************************************",
  symbol: "DEGEN",
  decimals: 18,
  image: "https://www.betswirl.com/img/tokens/DEGEN.svg"
}

const ETH_TOKEN: TokenWithImage = {
  address: "******************************************",
  symbol: "ETH",
  decimals: 18,
  image: "https://www.betswirl.com/img/tokens/ETH.svg"
}

// Optional: Limit available tokens to specific ones
const ALLOWED_TOKENS = [
  DEGEN_TOKEN.address,
  ETH_TOKEN.address,
  "******************************************", // USDC
]

createRoot(document.getElementById('root')!).render(
  <WagmiProvider config={config}>
    <QueryClientProvider client={queryClient}>
      <OnchainKitProvider chain={base} config={onChainKitConfig}>
        <BetSwirlSDKProvider
          initialChainId={base.id}
          bankrollToken={DEGEN_TOKEN}     // Optional: set default betting token
          filteredTokens={ALLOWED_TOKENS} // Optional: limit available tokens
        >
          <App />
        </BetSwirlSDKProvider>
      </OnchainKitProvider>
    </QueryClientProvider>
  </WagmiProvider>,
)
```

### Add Game Component

In `src/App.tsx`:

```tsx
import { CoinTossGame } from '@betswirl/ui'

// Add component
<div style={{ margin: '2rem 0' }}>
  <CoinTossGame />
</div>
```

### Run

```shell
npm run dev
```

Open [http://localhost:5173](http://localhost:5173)

## Deploy

### Push to GitHub

```shell
git init
git add .
git commit -m "Add BetSwirl casino game"
```

### Deploy to Vercel

* Sign in to [vercel.com](https://vercel.com) with GitHub
* Click "Add New..." → "Project"
* Select your repository
* Click "Import" → "Deploy"
* Get public URL after ~2 minutes

## Configuration

### BetSwirlSDKProvider Props

You can customize the SDK behavior with these optional props:

```tsx
<BetSwirlSDKProvider
  initialChainId={base.id}
  affiliate="******************************************"  // Your affiliate address
  bankrollToken={customToken}                            // Default betting token
  filteredTokens={["0x...", "0x..."]}                   // Limit available tokens
>
  <App />
</BetSwirlSDKProvider>
```

| Prop | Type | Description |
|------|------|-------------|
| `initialChainId` | `number` | **Required.** Chain ID to initialize the SDK with (currently only Base: 8453) |
| `affiliate` | `string` | Optional. Your wallet address to receive affiliate commissions. If not provided, default affiliate will be used |
| `bankrollToken` | `TokenWithImage` | Optional. Default token for betting. Must include `address`, `symbol`, `decimals`, and `image` properties. [See available tokens →](./checking-available-tokens.md) |
| `filteredTokens` | `string[]` | Optional. Array of token addresses to limit which tokens are available for selection. If not provided, all supported tokens will be available. [Learn more about token filtering →](./checking-available-tokens.md#token-filtering) |

#### TokenWithImage Interface

```tsx
interface TokenWithImage {
  address: string      // Token contract address (use "******************************************" for native token)
  symbol: string       // Token symbol (e.g., "ETH", "DEGEN", "USDC")
  decimals: number     // Token decimals (18 for most tokens, 6 for USDC)
  image: string        // URL to token icon image
}
```

### Environment Variables

**You do NOT need any environment variables** when using this library. All configuration is done through React props.

### Advanced Configuration (Optional)

If you want to use a custom RPC endpoint for better performance, you can set:

```env
VITE_RPC_URL=https://your-custom-base-rpc.com
```

This library currently supports **Base network only**. The custom RPC URL will be used instead of the default Base RPC (`https://mainnet.base.org`).

**When to use custom RPC:**
- You have a premium RPC provider (Alchemy, Infura, etc.)
- You want better reliability or speed
- You're hitting rate limits on the default public RPC

**When NOT needed:**
- For most applications the default RPC works fine
- If you're just testing or getting started

## Common Issues and Solutions

### TypeScript Errors

**Problem:** `Type 'string' is not assignable to type 'TokenWithImage'`

```tsx
// ❌ Wrong - missing required properties
<BetSwirlSDKProvider bankrollToken="DEGEN" />

// ✅ Correct - complete TokenWithImage object
const DEGEN_TOKEN: TokenWithImage = {
  address: "******************************************",
  symbol: "DEGEN",
  decimals: 18,
  image: "https://www.betswirl.com/img/tokens/DEGEN.svg"
}
<BetSwirlSDKProvider bankrollToken={DEGEN_TOKEN} />
```

**Problem:** `Property 'initialChainId' is missing`

```tsx
// ❌ Wrong - missing required prop
<BetSwirlSDKProvider>
  <App />
</BetSwirlSDKProvider>

// ✅ Correct - include required initialChainId
<BetSwirlSDKProvider initialChainId={base.id}>
  <App />
</BetSwirlSDKProvider>
```

### Token Configuration Issues

**Problem:** Token not appearing in the selection list

- Verify the token address is correct and matches the Bank contract
- Check that `allowed = true` and `paused = false` in the Bank contract
- Ensure the token is not filtered out by `filteredTokens` prop
- If using `filteredTokens`, make sure the token address is included in the array

**Problem:** Too many tokens in the selection list

Use `filteredTokens` to limit available options:
```tsx
// ✅ Limit to specific tokens for better UX
<BetSwirlSDKProvider
  initialChainId={base.id}
  filteredTokens={["******************************************", "******************************************"]}
>
  <App />
</BetSwirlSDKProvider>
```

**Problem:** Token icon not displaying

- Verify the `image` URL is accessible and returns a valid image
- Use the standard BetSwirl token image pattern: `https://www.betswirl.com/img/tokens/{SYMBOL}.svg`
- Ensure the image URL supports CORS for web applications

## Example Result

* **GitHub Repository**: [github.com/chainhackers/betswirl-ui-react-demo](https://github.com/chainhackers/betswirl-ui-react-demo)
* **Live Demo**: [betswirl-ui-react-demo.vercel.app](https://betswirl-ui-react-demo.vercel.app)

